# MMDetection 模型推理
from pathlib import Path

import geopandas as gpd
import numpy as np
import rasterio
import torch
from mmdet.apis import DetInferencer
from mmdet.utils import register_all_modules
from shapely.geometry import Point

register_all_modules()


def main(model_name, config_file, checkpoint_file, img_dir, output_dir, device, score_threshold):
    img_paths = list(str(i) for i in img_dir.glob("*.tif"))

    crses, transforms = [], []
    for img in img_paths:
        with rasterio.open(img) as src:
            transforms.append(src.transform)
            crses.append(src.crs)

    inferencer = DetInferencer(config_file, checkpoint_file, device=device)

    results = inferencer(img_paths, batch_size=48)["predictions"]

    for img, transform, crs, result in zip(img_paths, transforms, crses, results):
        probs = np.array(result["scores"])
        bboxes = np.array(result["bboxes"])
        keep = probs > score_threshold
        bboxes = bboxes[keep]
        probs = probs[keep]

        if len(probs) == 0 or len(bboxes) == 0:
            continue

        centers = (bboxes[:, :2] + bboxes[:, 2:]) / 2
        transformed_coords = transform * (centers[:, 0], centers[:, 1])
        points = [Point(x, y) for x, y in zip(*transformed_coords)]

        output_file = gpd.GeoDataFrame(geometry=points, crs=crs).to_crs(32647)
        output_file["score"] = probs.reshape(-1, 1)

        output_path = output_dir / f"{Path(img).stem}_{model_name}.arrow"
        output_file.to_file(output_path, encoding="utf-8", driver="Arrow")


def run():
    model_name = "deformable_detr"
    model_cfg = r"work_dirs\deformable_detr\deformable_detr.py"
    model_ckpt = r"work_dirs\deformable_detr\epoch_300.pth"

    # model_name = "yolox"
    # model_cfg = rf"work_dirs\config\{model_name}\vis_data\config.py"
    # model_ckpt = rf"work_dirs\config\{model_name}\epoch_250.pth"

    model_name = "centernet"
    model_cfg = r"work_dirs\centernet_worse\centernet_worse.py"
    model_ckpt = r"work_dirs\centernet_worse\epoch_50.pth"

    # model_name = "centernet_great"
    # model_cfg = r"work_dirs\config\centernet\vis_data\config.py"
    # model_ckpt = r"work_dirs\config\centernet\epoch_300.pth"

    # model_name = "yolox"
    # model_cfg = r"work_dirs\yolox\yolox.py"
    # model_ckpt = r"work_dirs\yolox\20250701_142126\epoch_250.pth"

    # model_name = "faster_rcnn"
    # model_cfg = r"work_dirs\config\20250630_155425_faster_rcnn\vis_data\config.py"
    # model_ckpt = r"work_dirs\config\20250630_155425_faster_rcnn\epoch_300.pth"

    img_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch2_384\images")
    output_dir = Path(rf"C:\Projects\tobacco\tobacco_det_data\batch2_384\approach\{model_name}\inference")
    device = "cpu"
    # device = "cuda" if torch.cuda.is_available() else "cpu"
    score_threshold = 0.4

    output_dir.mkdir(parents=True, exist_ok=True)
    main(
        model_name,
        model_cfg,
        model_ckpt,
        img_dir,
        output_dir,
        device,
        score_threshold,
    )
    print(f"Output saved to {output_dir}")


if __name__ == "__main__":
    run()
