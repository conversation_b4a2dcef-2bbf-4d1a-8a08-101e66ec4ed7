#!/usr/bin/env python3
"""
Point Detection Evaluation Script

This script evaluates point detection results by:
1. Loading inference results (Point geometries in UTM coordinates)
2. Loading ground truth (bbox/polygon geometries, using their centers)
3. Reprojecting ground truth to match inference CRS (UTM)
4. Using Hungarian algorithm to match points
5. Calculating mean distance and counting missed points
6. Discarding point pairs with distance > 0.15m (15cm)

Usage:
    python evaluate.py <inference_shp> <ground_truth_shp>
"""

import warnings
from pathlib import Path
from typing import Any, Dict

import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy.spatial import distance_matrix
from shapely.geometry import LineString
from tqdm import tqdm
from multiprocessing import Pool

plt.style.use("ggplot")  # 应用主题
# 设置全局字体为Times New Roman
plt.rcParams["font.family"] = "serif"
plt.rcParams["font.serif"] = ["Times New Roman"]

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

target_crs = "EPSG:32647"


def load_inference_points(inference_shp_path: str) -> gpd.GeoSeries:
    """
    Load inference results from shapefile.

    Args:
        inference_shp_path: Path to inference shapefile containing Point geometries

    Returns:
        GeoDataFrame with Point geometries in UTM coordinates
    """
    inference_gdf = gpd.read_file(inference_shp_path).to_crs(target_crs)
    inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]

    # print(f"Loaded {len(inference_gdf)} inference points")
    # print(f"Inference CRS: {inference_gdf.crs}")

    # Verify all geometries are Points
    non_points = inference_gdf[~inference_gdf.geometry.geom_type.eq("Point")]
    if len(non_points) > 0:
        print(f"Warning: Found {len(non_points)} non-Point geometries in inference data")
        inference_gdf = inference_gdf[inference_gdf.geometry.geom_type.eq("Point")]
        print(f"Filtered to {len(inference_gdf)} Point geometries")

    return inference_gdf.geometry


def load_ground_truth_points(gt_path: str, target_crs: Any) -> gpd.GeoSeries:
    """
    Load ground truth from shapefile and convert to center points.

    Args:
        gt_path: Path to ground truth shapefile containing bbox/polygon geometries
        target_crs: Target CRS to reproject to (should match inference CRS)

    Returns:
        GeoDataFrame with Point geometries representing centers of ground truth bboxes
    """
    # Load ground truth shapefile
    gt_gdf = gpd.read_file(gt_path).to_crs(target_crs)
    gt_gdf = gt_gdf[gt_gdf.geometry.geom_type.eq("Point")]

    # print(f"Loaded {len(gt_gdf)} ground truth geometries")
    # print(f"Ground truth CRS: {gt_gdf.crs}")

    # Convert geometries to center points
    # print("Converting ground truth geometries to center points...")
    gt_points_gdf = gt_gdf.centroid

    # print(f"Created {len(gt_points_gdf)} ground truth center points")
    return gt_points_gdf


def calculate_distance_matrix(inference_points: gpd.GeoSeries, gt_points: gpd.GeoSeries) -> np.ndarray:
    """
    Calculate distance matrix between inference points and ground truth points.

    Args:
        inference_points: GeoSeries with inference Point geometries
        gt_points: GeoSeries with ground truth Point geometries

    Returns:
        Distance matrix of shape (n_inference, n_gt) in meters
    """
    # print("Calculating distance matrix...")

    # Extract coordinates
    inf_coords = np.array([inference_points.x, inference_points.y]).T
    gt_coords = np.array([gt_points.x, gt_points.y]).T
    distances = distance_matrix(inf_coords, gt_coords)

    # print(f"Distance matrix shape: {distances.shape}")
    # print(f"Min distance: {distances.min():.3f}m, Max distance: {distances.max():.3f}m")

    return distances


def greedy_match(cost_matrix: np.ndarray, max_distance: float):
    """
    对给定的代价矩阵执行贪婪最小距离匹配。

    参数
    ----
    cost_matrix : np.ndarray, shape (m, n)
        代价矩阵，其中 cost_matrix[i, j] 表示第 i 个点与第 j 个点之间的代价（距离）。

    返回
    ----
    matches : List[Tuple[int, int]]
        匹配结果列表，每个元组 (i, j) 表示第 i 行与第 j 列的点被匹配。
    """
    m, n = cost_matrix.shape
    # 生成 (i, j, distance) 三元组列表
    all_pairs = [(i, j, cost_matrix[i, j]) for i in range(m) for j in range(n) if cost_matrix[i, j] <= max_distance]
    # 按距离从小到大排序
    all_pairs.sort(key=lambda x: x[2])

    matched_rows = set()
    matched_cols = set()
    matches = []

    # 依次挑选最小距离的可用配对
    for i, j, dist in all_pairs:
        if i not in matched_rows and j not in matched_cols:
            matched_rows.add(i)
            matched_cols.add(j)
            matches.append((i, j))
            # 当一方所有点都已匹配，可提前结束
            if len(matched_rows) == m or len(matched_cols) == n:
                break

    matched_indices = np.array(matches)

    if len(matched_indices) == 0:
        return np.array([]), np.array([]), np.array([])

    inf_indices = matched_indices[:, 0]
    gt_indices = matched_indices[:, 1]
    matched_distances = cost_matrix[inf_indices, gt_indices]

    return inf_indices, gt_indices, matched_distances


def create_matching_lines(
    inference_points: gpd.GeoSeries,
    gt_points: gpd.GeoSeries,
    matched_inf_idx: np.ndarray,
    matched_gt_idx: np.ndarray,
    matched_distances: np.ndarray,
    output_path,
    target_crs="EPSG:32647",
) -> None:
    """
    Create polylines between matched inference and ground truth points.

    Args:
        inference_points: GeoSeries with inference Point geometries
        gt_points: GeoSeries with ground truth Point geometries
        matched_inf_idx: Indices of matched inference points
        matched_gt_idx: Indices of matched ground truth points
        matched_distances: Distances of matched pairs
        output_path: Path to save the polyline shapefile
        target_crs: Target CRS for the output shapefile
    """

    if len(matched_inf_idx) == 0:
        print("No matched pairs found, skipping polyline creation.")
        return

    # Create LineString geometries for each matched pair
    lines = []
    line_data = []

    for i, (inf_idx, gt_idx) in enumerate(zip(matched_inf_idx, matched_gt_idx)):
        # Get the matched points
        inf_point = inference_points.iloc[inf_idx]
        gt_point = gt_points.iloc[gt_idx]

        # Create LineString connecting the two points
        line = LineString([(inf_point.x, inf_point.y), (gt_point.x, gt_point.y)])

        lines.append(line)
        line_data.append(
            {
                "pair_id": i + 1,
                "inf_x": inf_point.x,
                "inf_y": inf_point.y,
                "gt_x": gt_point.x,
                "gt_y": gt_point.y,
                "inf_idx": inf_idx,
                "gt_idx": gt_idx,
            }
        )

    # Create GeoDataFrame with the lines
    lines_gdf = gpd.GeoDataFrame(line_data, geometry=lines, crs=target_crs)

    # Save to shapefile
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    lines_gdf.to_file(output_path, encoding="utf-8", driver="Arrow")

    # print(f"Saved {len(lines_gdf)} polylines to: {output_path}")
    # print("Polyline attributes:")
    # print("  - pair_id: Sequential ID for each matched pair")
    # print("  - distance: Distance between matched points (meters)")
    # print("  - inf_x, inf_y: Inference point coordinates")
    # print("  - gt_x, gt_y: Ground truth point coordinates")
    # print("  - inf_idx, gt_idx: Original indices in the input shapefiles")


def evaluate_detection(
    inference_path: Path, gt_shp_path: Path, max_distance: float, line_output_path: Path
) -> Dict[str, Any]:
    """
    Evaluate point detection performance.

    Args:
        inference_shp_path: Path to inference shapefile
        gt_shp_path: Path to ground truth shapefile
        max_distance: Maximum distance threshold for valid matches (meters)
        line_output_path: Optional path to save polylines between matched pairs

    Returns:
        Dictionary containing evaluation metrics
    """
    inference_gdf = load_inference_points(inference_path)
    gt_gdf = load_ground_truth_points(gt_shp_path, inference_gdf.crs)

    if len(inference_gdf) == 0:
        print("Error: No inference points found!")
        return {"error": "No inference points"}

    if len(gt_gdf) == 0:
        print("Error: No ground truth points found!")
        return {"error": "No ground truth points"}

    # print("\nEvaluation setup:")
    # print(f"  Inference points: {len(inference_gdf)}")
    # print(f"  Ground truth points: {len(gt_gdf)}")
    # print(f"  Max distance threshold: {max_distance}m\n")

    # Calculate distance matrix
    distance_matrix = calculate_distance_matrix(inference_gdf, gt_gdf)

    # Perform Hungarian matching
    # inf_indices, gt_indices, matched_distances = hungarian_matching(distance_matrix, max_distance)
    inf_indices, gt_indices, matched_distances = greedy_match(distance_matrix, max_distance)

    # Calculate metrics
    n_inference = len(inference_gdf)
    n_ground_truth = len(gt_gdf)
    n_matched = len(matched_distances)
    n_missed_gt = n_ground_truth - n_matched
    n_false_positives = n_inference - n_matched

    # Calculate mean distance for valid matches
    mean_distance = np.mean(matched_distances) if n_matched > 0 else 0.0

    # Calculate precision, recall, F1
    precision = n_matched / n_inference if n_inference > 0 else 0.0
    recall = n_matched / n_ground_truth if n_ground_truth > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

    # Create polylines between matched pairs
    if line_output_path and n_matched > 0:
        create_matching_lines(
            inference_gdf, gt_gdf, inf_indices, gt_indices, matched_distances, line_output_path, target_crs
        )

    # Prepare results
    results = {
        "name": inference_path.name,
        "n_inference": n_inference,
        "n_ground_truth": n_ground_truth,
        "n_matched": n_matched,
        "n_missed_gt": n_missed_gt,
        "n_false_positives": n_false_positives,
        "mean_distance": mean_distance,
        "max_distance_threshold": max_distance,
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        # "matched_distances": matched_distances.tolist() if n_matched > 0 else [],
    }

    return results


def get_sum_value(df):
    TP = df["n_matched"].sum()
    TP_FP = df["n_inference"].sum()
    TP_FN = df["n_ground_truth"].sum()
    precision = TP / TP_FP
    recall = TP / TP_FN
    f1_score = 2 * (precision * recall) / (precision + recall)

    sum_value = [
        "sum",
        df["n_inference"].sum(),
        df["n_ground_truth"].sum(),
        df["n_matched"].sum(),
        df["n_missed_gt"].sum(),
        df["n_false_positives"].sum(),
        df["mean_distance"].mean(),
        df["max_distance_threshold"].mean(),
        precision,
        recall,
        f1_score,
    ]

    return sum_value


def collect_reults(results, sums_data, output_path: Path):
    results.loc["sum"] = sums_data
    results.sort_values(by="name").round(4).to_csv(output_path, index=False)


def save_sums_data(sums_data, name, output_path: Path):
    for i in sums_data:
        i[0] = name

    sums_data = pd.DataFrame(
        sums_data,
        columns=[
            "name",
            "n_inference",
            "n_ground_truth",
            "n_matched",
            "n_missed_gt",
            "n_false_positives",
            "mean_distance",
            "max_distance_threshold",
            "precision",
            "recall",
            "f1_score",
        ],
    )
    sums_data.sort_values(by="max_distance_threshold").round(4).to_csv(output_path, index=False)


def draw_f1_curve(sums_data, steps, output_path):
    """
    Draw Recall-Confidence (Distance Threshold) curve with elegant styling.

    Args:
        sums_data: List of summary data for each distance threshold
        steps: Array of distance thresholds used
        output_path: Path to save the figure
    """
    f1 = [i[-1] for i in sums_data]

    # Create figure with elegant styling
    fig, ax = plt.subplots(figsize=(12, 8))

    # Plot the F1 curve with smooth line and markers
    ax.plot(
        steps,
        f1,
        "o-",
        linewidth=3,
        markersize=8,
        color="#3A86FF",
        markerfacecolor="#06FFA5",
        markeredgecolor="white",
        markeredgewidth=2,
        alpha=0.9,
        label="F1 vs Distance Threshold",
    )

    # Add filled area under the curve for visual appeal
    ax.fill_between(steps, f1, alpha=0.2, color="#3A86FF")

    # Add grid with subtle styling
    ax.grid(True, alpha=0.3, linestyle="--", linewidth=0.8)

    # Customize axes
    ax.set_xlabel("Distance Threshold (meters)", fontsize=14, fontweight="bold")
    ax.set_ylabel("F1", fontsize=14, fontweight="bold")
    ax.set_title("F1 vs Distance Threshold\nTobacco Detection Performance", fontsize=16, fontweight="bold", pad=20)

    # Set axis limits with padding
    ax.set_xlim(steps.min() - 0.01, steps.max() + 0.01)
    ax.set_ylim(-0.05, 1.05)

    # Customize tick parameters
    ax.tick_params(axis="both", which="major", labelsize=12, width=1.5, length=6)
    ax.tick_params(axis="both", which="minor", width=1, length=3)

    # Add legend with elegant styling
    legend = ax.legend(loc="best", fontsize=12, frameon=True, fancybox=True, shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor("white")
    legend.get_frame().set_edgecolor("gray")

    # Add text box with statistics
    # max_f1 = max(f1)
    # textstr = f"Max F1: {max_f1:.3f}"
    # props = dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8)
    # ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=11, verticalalignment="top", bbox=props)

    # Enhance the overall appearance
    fig.patch.set_facecolor("white")
    ax.set_facecolor("#FAFAFA")

    # Save with high quality
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches="tight", facecolor="white", edgecolor="none")
    plt.close()


def main(approach):
    max_distances = np.arange(0.02, 0.4, 0.02)
    ground_truth_dir = Path(r"C:\projects\tobacco\tobacco_det_data\batch2_384\ground_truth\ground_truth_center")
    inference_dir = Path(rf"C:\projects\tobacco\tobacco_det_data\batch2_384\approach\{approach}\inference")
    home_dir = Path(rf"C:\projects\tobacco\tobacco_det_data\batch2_384\approach\{approach}\result")

    sums_data = []
    for max_distance in tqdm(max_distances, desc=f"Processing {approach}"):
        results = []
        subdir = home_dir / f"max_distance_{max_distance:.2f}"
        Path(subdir, "line").mkdir(parents=True, exist_ok=True)

        for gt in ground_truth_dir.glob("*.arrow"):
            # 记得改这里后缀
            infer = inference_dir / gt.name.replace("_gt.arrow", f"_{approach}.arrow")
            line = subdir / "line" / gt.name.replace("_gt.arrow", f"_{approach}.arrow")

            result = evaluate_detection(infer, gt, max_distance, line)
            results.append(result)

        results = pd.DataFrame(results)
        results_sum = get_sum_value(results)

        sums_data.append(results_sum)
        collect_reults(results, results_sum, subdir / "results.csv")

    save_sums_data(sums_data, approach, home_dir / "sums_data.csv")
    draw_f1_curve(sums_data, max_distances, home_dir / "f1_curve.pdf")


def main_batch():
    approachs = ["deformable_detr", "faster_rcnn", "centernet", "yolox", "yolo11s", "spectral", "kmeans"]

    # Multi-processing
    with Pool(processes=8) as pool:
        pool.map(main, ["centernet"])


if __name__ == "__main__":
    main_batch()
