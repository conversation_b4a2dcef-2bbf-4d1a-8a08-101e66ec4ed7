import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt

plt.style.use("ggplot")  # 应用主题
# 设置全局字体为Times New Roman
plt.rcParams["font.family"] = "serif"
plt.rcParams["font.serif"] = ["Times New Roman"]


def draw_r_curve(sums_data, steps, output_path):
    """
    Draw Recall-Confidence (Distance Threshold) curve with elegant styling.

    Args:
        sums_data: List of summary data for each distance threshold
        steps: Array of distance thresholds used
        output_path: Path to save the figure
    """
    # Create figure with elegant styling
    fig, ax = plt.subplots(figsize=(12, 8))

    for name, info in sums_data.items():
        recall = info["recall"]
        color = info["color"]
        markerfacecolor = info["markerfacecolor"]

        ax.plot(
            steps,
            recall,
            "o-",
            linewidth=3,
            markersize=8,
            color=color,
            markerfacecolor=markerfacecolor,
            markeredgecolor="white",
            markeredgewidth=2,
            alpha=0.9,
            label=name,
        )

    # Add filled area under the curve for visual appeal
    # ax.fill_between(steps, f1, alpha=0.2, color="#3A86FF")

    # Add grid with subtle styling
    ax.grid(True, alpha=0.3, linestyle="--", linewidth=0.8)

    # Customize axes
    ax.set_xlabel("Distance Threshold (meters)", fontsize=14, fontweight="bold")
    ax.set_ylabel("Recall", fontsize=14, fontweight="bold")
    ax.set_title("Recall vs Distance Threshold\nTobacco Detection Performance", fontsize=16, fontweight="bold", pad=20)

    # Set axis limits with padding
    ax.set_xlim(steps.min() - 0.01, steps.max() + 0.01)
    ax.set_ylim(-0.05, 1.05)

    # Customize tick parameters
    ax.tick_params(axis="both", which="major", labelsize=12, width=1.5, length=6)
    ax.tick_params(axis="both", which="minor", width=1, length=3)

    # Add legend with elegant styling
    legend = ax.legend(loc="best", fontsize=12, frameon=True, fancybox=True, shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor("white")
    legend.get_frame().set_edgecolor("gray")

    # Enhance the overall appearance
    fig.patch.set_facecolor("white")
    ax.set_facecolor("#FAFAFA")

    # Save with high quality
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches="tight", facecolor="white", edgecolor="none")
    plt.close()


def draw_p_curve(sums_data, steps, output_path):
    """
    Draw Precision-Confidence (Distance Threshold) curve with elegant styling.

    Args:
        sums_data: List of summary data for each distance threshold
        steps: Array of distance thresholds used
        output_path: Path to save the figure
    """
    # Create figure with elegant styling
    fig, ax = plt.subplots(figsize=(12, 8))

    for name, info in sums_data.items():
        precision = info["precision"]
        color = info["color"]
        markerfacecolor = info["markerfacecolor"]

        ax.plot(
            steps,
            precision,
            "o-",
            linewidth=3,
            markersize=8,
            color=color,
            markerfacecolor=markerfacecolor,
            markeredgecolor="white",
            markeredgewidth=2,
            alpha=0.9,
            label=name,
        )

    # Add filled area under the curve for visual appeal
    # ax.fill_between(steps, f1, alpha=0.2, color="#3A86FF")

    # Add grid with subtle styling
    ax.grid(True, alpha=0.3, linestyle="--", linewidth=0.8)

    # Customize axes
    ax.set_xlabel("Distance Threshold (meters)", fontsize=14, fontweight="bold")
    ax.set_ylabel("Precision", fontsize=14, fontweight="bold")
    ax.set_title(
        "Precision vs Distance Threshold\nTobacco Detection Performance", fontsize=16, fontweight="bold", pad=20
    )

    # Set axis limits with padding
    ax.set_xlim(steps.min() - 0.01, steps.max() + 0.01)
    ax.set_ylim(-0.05, 1.05)

    # Customize tick parameters
    ax.tick_params(axis="both", which="major", labelsize=12, width=1.5, length=6)
    ax.tick_params(axis="both", which="minor", width=1, length=3)

    # Add legend with elegant styling
    legend = ax.legend(loc="best", fontsize=12, frameon=True, fancybox=True, shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor("white")
    legend.get_frame().set_edgecolor("gray")

    # Add text box with statistics
    # max_f1 = max(f1)
    # textstr = f"Max F1: {max_f1:.3f}"
    # props = dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8)
    # ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=11, verticalalignment="top", bbox=props)

    # Enhance the overall appearance
    fig.patch.set_facecolor("white")
    ax.set_facecolor("#FAFAFA")

    # Save with high quality
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches="tight", facecolor="white", edgecolor="none")
    plt.close()


def draw_f1_curve(sums_data, steps, output_path):
    """
    Draw Recall-Confidence (Distance Threshold) curve with elegant styling.

    Args:
        sums_data: List of summary data for each distance threshold
        steps: Array of distance thresholds used
        output_path: Path to save the figure
    """
    # Create figure with elegant styling
    fig, ax = plt.subplots(figsize=(12, 8))

    for name, info in sums_data.items():
        f1 = info["f1_score"]
        color = info["color"]
        markerfacecolor = info["markerfacecolor"]

        ax.plot(
            steps,
            f1,
            "o-",
            linewidth=3,
            markersize=8,
            color=color,
            markerfacecolor=markerfacecolor,
            markeredgecolor="white",
            markeredgewidth=2,
            alpha=0.9,
            label=name,
        )

    # Add filled area under the curve for visual appeal
    # ax.fill_between(steps, f1, alpha=0.2, color="#3A86FF")

    # Add grid with subtle styling
    ax.grid(True, alpha=0.3, linestyle="--", linewidth=0.8)

    # Customize axes
    ax.set_xlabel("Distance Threshold (meters)", fontsize=14, fontweight="bold")
    ax.set_ylabel("F1", fontsize=14, fontweight="bold")
    ax.set_title("F1 vs Distance Threshold\nTobacco Detection Performance", fontsize=16, fontweight="bold", pad=20)

    # Set axis limits with padding
    ax.set_xlim(steps.min() - 0.01, steps.max() + 0.01)
    ax.set_ylim(-0.05, 1.05)

    # Customize tick parameters
    ax.tick_params(axis="both", which="major", labelsize=12, width=1.5, length=6)
    ax.tick_params(axis="both", which="minor", width=1, length=3)

    # Add legend with elegant styling
    legend = ax.legend(loc="best", fontsize=12, frameon=True, fancybox=True, shadow=True, framealpha=0.9)
    legend.get_frame().set_facecolor("white")
    legend.get_frame().set_edgecolor("gray")

    # Add text box with statistics
    # max_f1 = max(f1)
    # textstr = f"Max F1: {max_f1:.3f}"
    # props = dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8)
    # ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=11, verticalalignment="top", bbox=props)

    # Enhance the overall appearance
    fig.patch.set_facecolor("white")
    ax.set_facecolor("#FAFAFA")

    # Save with high quality
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches="tight", facecolor="white", edgecolor="none")
    plt.close()


def main():
    """Main function to run the evaluation."""
    max_distances = np.arange(0.02, 0.4, 0.02)
    home_dir = Path(r"C:\projects\tobacco\tobacco_det_data\batch2_384\approach")

    info_set = {
        "spectral": {
            "color": "#000000",
            "markerfacecolor": "#FF06C7",
        },
        "kmeans": {
            "color": "#515151",
            "markerfacecolor": "#06C7FF",
        },
        "centernet": {
            "color": "#FF06C7",
            "markerfacecolor": "#FFB700",
        },
        "faster_rcnn": {
            "color": "#06FFA5",
            "markerfacecolor": "#FF0606",
        },
        "deformable_detr": {
            "color": "#007B10",
            "markerfacecolor": "#BF6666",
        },
        "yolox": {
            "color": "#3A86FF",
            "markerfacecolor": "#DFFD00",
        },
        "yolo11s": {
            "color": "#282AC9",
            "markerfacecolor": "#C06C44",
        },
        "centernet_great": {
            "color": "#282AC9",
            "markerfacecolor": "#C06C44",
        },
    }

    for directory in home_dir.iterdir():
        if directory.is_dir() and directory.name in info_set:
            df = pd.read_csv(directory / "result/sums_data.csv")
            info_set[directory.name]["f1_score"] = df["f1_score"].values
            info_set[directory.name]["precision"] = df["precision"].values
            info_set[directory.name]["recall"] = df["recall"].values

    draw_f1_curve(info_set, max_distances, home_dir / "f1_curve.pdf")
    draw_p_curve(info_set, max_distances, home_dir / "p_curve.pdf")
    draw_r_curve(info_set, max_distances, home_dir / "r_curve.pdf")


if __name__ == "__main__":
    main()
