#!/usr/bin/env python3
"""
COCO JSON Validation Script

This script validates detection results by comparing prediction COCO JSON files
against ground truth COCO JSON files using the standard COCO evaluation metrics.

Features:
- Loads ground truth and prediction COCO JSON files
- Validates prediction format and content
- Computes standard COCO metrics (mAP, mAP@50, mAP@75, etc.)
- Provides detailed per-category analysis
- Generates comprehensive evaluation report
- Supports custom IoU thresholds and confidence score filtering

Usage:
    python box_validation.py --gt <ground_truth.json> --pred <predictions.json>
    python box_validation.py --gt gt.json --pred pred.json --score-thr 0.3 --output results.txt
"""

import argparse
import json
import sys
from collections import defaultdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

try:
    from pycocotools.coco import COCO
    from pycocotools.cocoeval import COCOeval
except ImportError:
    print("Error: pycocotools is required. Install with: pip install pycocotools")
    sys.exit(1)


def validate_coco_format(coco_data: Dict, data_type: str = "ground_truth") -> bool:
    """
    Validate COCO format structure.

    Args:
        coco_data: Dictionary containing COCO format data
        data_type: Type of data being validated ("ground_truth" or "predictions")

    Returns:
        True if valid, False otherwise
    """
    required_keys = ["images", "annotations", "categories"]

    # Check required top-level keys
    for key in required_keys:
        if key not in coco_data:
            print(f"Error: Missing required key '{key}' in {data_type}")
            return False

    # Validate images
    if not isinstance(coco_data["images"], list):
        print(f"Error: 'images' must be a list in {data_type}")
        return False

    for img in coco_data["images"]:
        required_img_keys = ["id", "file_name", "width", "height"]
        for key in required_img_keys:
            if key not in img:
                print(f"Error: Missing required image key '{key}' in {data_type}")
                return False

    # Validate annotations
    if not isinstance(coco_data["annotations"], list):
        print(f"Error: 'annotations' must be a list in {data_type}")
        return False

    for ann in coco_data["annotations"]:
        required_ann_keys = ["id", "image_id", "category_id", "bbox"]
        for key in required_ann_keys:
            if key not in ann:
                print(f"Error: Missing required annotation key '{key}' in {data_type}")
                return False

        # Validate bbox format [x, y, width, height]
        bbox = ann["bbox"]
        if not isinstance(bbox, list) or len(bbox) != 4:
            print(f"Error: Invalid bbox format in {data_type}. Expected [x, y, width, height]")
            return False

        if any(not isinstance(val, (int, float)) for val in bbox):
            print(f"Error: Bbox values must be numeric in {data_type}")
            return False

        if bbox[2] <= 0 or bbox[3] <= 0:
            print(f"Warning: Invalid bbox dimensions (w={bbox[2]}, h={bbox[3]}) in {data_type}")

    # Validate categories
    if not isinstance(coco_data["categories"], list):
        print(f"Error: 'categories' must be a list in {data_type}")
        return False

    for cat in coco_data["categories"]:
        required_cat_keys = ["id", "name"]
        for key in required_cat_keys:
            if key not in cat:
                print(f"Error: Missing required category key '{key}' in {data_type}")
                return False

    return True


def validate_prediction_format(pred_data) -> Tuple[bool, str]:
    """
    Validate prediction format for COCO evaluation.
    Supports both list format and full COCO format.

    Args:
        pred_data: Either list of prediction dictionaries or full COCO format dict

    Returns:
        Tuple of (is_valid, format_type)
        format_type: "list" or "coco"
    """
    # Check if it's a full COCO format
    if isinstance(pred_data, dict):
        if "annotations" in pred_data:
            print("Detected full COCO format predictions")
            # Validate as COCO format
            if not validate_coco_format(pred_data, "predictions"):
                return False, "coco"
            return True, "coco"
        else:
            print("Error: Dictionary format predictions must have 'annotations' key")
            return False, "unknown"

    # Check if it's a list format
    if not isinstance(pred_data, list):
        print("Error: Predictions must be a list of dictionaries or COCO format dictionary")
        return False, "unknown"

    print("Detected list format predictions")
    for pred in pred_data:
        required_keys = ["image_id", "category_id", "bbox"]

        for key in required_keys:
            if key not in pred:
                print(f"Error: Missing required prediction key '{key}'")
                return False, "list"

        # Validate bbox format
        bbox = pred["bbox"]
        if not isinstance(bbox, list) or len(bbox) != 4:
            print("Error: Invalid bbox format in predictions. Expected [x, y, width, height]")
            return False, "list"

        if any(not isinstance(val, (int, float)) for val in bbox):
            print("Error: Bbox values must be numeric in predictions")
            return False, "list"

        if bbox[2] <= 0 or bbox[3] <= 0:
            print(f"Warning: Invalid bbox dimensions (w={bbox[2]}, h={bbox[3]}) in predictions")

        # Validate score if present
        if "score" in pred:
            if not isinstance(pred["score"], (int, float)) or not (0 <= pred["score"] <= 1):
                print(f"Warning: Invalid score value {pred['score']}. Should be between 0 and 1")

    return True, "list"


def convert_coco_to_prediction_list(coco_data: Dict, default_score: float = 1.0) -> List[Dict]:
    """
    Convert COCO format annotations to prediction list format.

    Args:
        coco_data: COCO format dictionary with annotations
        default_score: Default confidence score to assign (since GT has no scores)

    Returns:
        List of prediction dictionaries
    """
    predictions = []

    for ann in coco_data["annotations"]:
        pred = {
            "image_id": ann["image_id"],
            "category_id": ann["category_id"],
            "bbox": ann["bbox"],
            "score": ann.get("score", default_score)  # Use provided score or default
        }
        predictions.append(pred)

    print(f"Converted {len(predictions)} COCO annotations to prediction format")
    return predictions


def filter_predictions_by_score(predictions: List[Dict], score_threshold: float) -> List[Dict]:
    """
    Filter predictions by confidence score threshold.

    Args:
        predictions: List of prediction dictionaries
        score_threshold: Minimum confidence score threshold

    Returns:
        Filtered list of predictions
    """
    filtered = [pred for pred in predictions if pred["score"] >= score_threshold]
    print(f"Filtered predictions: {len(predictions)} -> {len(filtered)} (threshold: {score_threshold})")
    return filtered


def compute_basic_statistics(gt_coco: COCO, predictions: List[Dict]) -> Dict[str, Any]:
    """
    Compute basic statistics about the dataset and predictions.

    Args:
        gt_coco: Ground truth COCO object
        predictions: List of prediction dictionaries

    Returns:
        Dictionary containing basic statistics
    """
    stats = {}

    # Ground truth statistics
    stats["gt_images"] = len(gt_coco.getImgIds())
    stats["gt_annotations"] = len(gt_coco.getAnnIds())
    stats["gt_categories"] = len(gt_coco.getCatIds())

    # Prediction statistics
    stats["pred_detections"] = len(predictions)
    pred_images = set(pred["image_id"] for pred in predictions)
    stats["pred_images"] = len(pred_images)

    # Per-category statistics
    gt_cat_counts = defaultdict(int)
    for ann_id in gt_coco.getAnnIds():
        ann = gt_coco.loadAnns([ann_id])[0]
        gt_cat_counts[ann["category_id"]] += 1

    pred_cat_counts = defaultdict(int)
    for pred in predictions:
        pred_cat_counts[pred["category_id"]] += 1

    stats["gt_per_category"] = dict(gt_cat_counts)
    stats["pred_per_category"] = dict(pred_cat_counts)

    return stats


def run_coco_evaluation(
    gt_coco: COCO,
    predictions: List[Dict],
    iou_thresholds: Optional[List[float]] = None,
    max_detections: Optional[List[int]] = None
) -> Tuple[COCOeval, Dict[str, float]]:
    """
    Run COCO evaluation on predictions.

    Args:
        gt_coco: Ground truth COCO object
        predictions: List of prediction dictionaries
        iou_thresholds: Custom IoU thresholds (default: [0.5:0.95:0.05])
        max_detections: Maximum detections per image (default: [1, 10, 100])

    Returns:
        Tuple of (COCOeval object, metrics dictionary)
    """
    if not predictions:
        print("Warning: No predictions to evaluate")
        return None, {}

    # Load predictions into COCO format
    try:
        pred_coco = gt_coco.loadRes(predictions)
    except Exception as e:
        print(f"Error loading predictions: {e}")
        return None, {}

    # Initialize COCO evaluator
    coco_eval = COCOeval(gt_coco, pred_coco, 'bbox')

    # Set custom parameters if provided
    if iou_thresholds is not None:
        coco_eval.params.iouThrs = np.array(iou_thresholds)

    if max_detections is not None:
        coco_eval.params.maxDets = max_detections

    # Run evaluation
    print("Running COCO evaluation...")
    coco_eval.evaluate()
    coco_eval.accumulate()
    coco_eval.summarize()

    # Extract metrics
    metrics = {
        'mAP': coco_eval.stats[0],
        'mAP@50': coco_eval.stats[1],
        'mAP@75': coco_eval.stats[2],
        'mAP_small': coco_eval.stats[3],
        'mAP_medium': coco_eval.stats[4],
        'mAP_large': coco_eval.stats[5],
        'AR@1': coco_eval.stats[6],
        'AR@10': coco_eval.stats[7],
        'AR@100': coco_eval.stats[8],
        'AR_small': coco_eval.stats[9],
        'AR_medium': coco_eval.stats[10],
        'AR_large': coco_eval.stats[11],
    }

    return coco_eval, metrics


def compute_per_category_metrics(coco_eval: COCOeval, gt_coco: COCO) -> Dict[str, Dict[str, float]]:
    """
    Compute per-category evaluation metrics.

    Args:
        coco_eval: COCOeval object after evaluation
        gt_coco: Ground truth COCO object

    Returns:
        Dictionary with per-category metrics
    """
    if coco_eval is None:
        return {}

    per_cat_metrics = {}

    # Get category information
    categories = gt_coco.loadCats(gt_coco.getCatIds())
    cat_id_to_name = {cat['id']: cat['name'] for cat in categories}

    # Compute per-category AP
    for cat_idx, cat_id in enumerate(gt_coco.getCatIds()):
        cat_name = cat_id_to_name[cat_id]

        # Extract AP for this category (IoU=0.5:0.95, area=all, maxDets=100)
        if coco_eval.eval is not None and 'precision' in coco_eval.eval:
            precision = coco_eval.eval['precision']
            # precision shape: [T, R, K, A, M] where T=IoU, R=recall, K=category, A=area, M=maxDets
            if precision.size > 0 and cat_idx < precision.shape[2]:
                cat_precision = precision[:, :, cat_idx, 0, -1]  # All IoU, all recall, this cat, all areas, max dets
                cat_ap = np.mean(cat_precision[cat_precision > -1])  # Average over valid entries
            else:
                cat_ap = 0.0
        else:
            cat_ap = 0.0

        per_cat_metrics[cat_name] = {
            'AP': cat_ap,
            'category_id': cat_id,
            'num_gt': len(gt_coco.getAnnIds(catIds=[cat_id]))
        }

    return per_cat_metrics


def generate_evaluation_report(
    stats: Dict[str, Any],
    metrics: Dict[str, float],
    per_cat_metrics: Dict[str, Dict[str, float]],
    output_file: Optional[str] = None
) -> str:
    """
    Generate a comprehensive evaluation report.

    Args:
        stats: Basic statistics dictionary
        metrics: Overall metrics dictionary
        per_cat_metrics: Per-category metrics dictionary
        output_file: Optional file path to save the report

    Returns:
        Report string
    """
    report_lines = []

    # Header
    report_lines.append("=" * 80)
    report_lines.append("COCO DETECTION EVALUATION REPORT")
    report_lines.append("=" * 80)
    report_lines.append("")

    # Basic Statistics
    report_lines.append("DATASET STATISTICS:")
    report_lines.append("-" * 40)
    report_lines.append(f"Ground Truth Images: {stats['gt_images']}")
    report_lines.append(f"Ground Truth Annotations: {stats['gt_annotations']}")
    report_lines.append(f"Number of Categories: {stats['gt_categories']}")
    report_lines.append(f"Prediction Images: {stats['pred_images']}")
    report_lines.append(f"Total Predictions: {stats['pred_detections']}")
    report_lines.append("")

    # Overall Metrics
    report_lines.append("OVERALL METRICS:")
    report_lines.append("-" * 40)
    for metric_name, value in metrics.items():
        report_lines.append(f"{metric_name:15}: {value:.4f}")
    report_lines.append("")

    # Per-category breakdown
    if per_cat_metrics:
        report_lines.append("PER-CATEGORY METRICS:")
        report_lines.append("-" * 40)
        report_lines.append(f"{'Category':<20} {'AP':<8} {'GT Count':<10}")
        report_lines.append("-" * 40)

        for cat_name, cat_metrics in per_cat_metrics.items():
            ap = cat_metrics['AP']
            gt_count = cat_metrics['num_gt']
            report_lines.append(f"{cat_name:<20} {ap:<8.4f} {gt_count:<10}")
        report_lines.append("")

    # Category distribution comparison
    report_lines.append("CATEGORY DISTRIBUTION:")
    report_lines.append("-" * 40)
    report_lines.append(f"{'Category':<20} {'GT Count':<10} {'Pred Count':<12}")
    report_lines.append("-" * 40)

    all_cats = set(stats['gt_per_category'].keys()) | set(stats['pred_per_category'].keys())
    for cat_id in sorted(all_cats):
        gt_count = stats['gt_per_category'].get(cat_id, 0)
        pred_count = stats['pred_per_category'].get(cat_id, 0)
        report_lines.append(f"Category {cat_id:<12} {gt_count:<10} {pred_count:<12}")

    report_lines.append("")
    report_lines.append("=" * 80)

    report = "\n".join(report_lines)

    # Save to file if specified
    if output_file:
        with open(output_file, 'w') as f:
            f.write(report)
        print(f"Report saved to: {output_file}")

    return report


def load_json_file(file_path: str) -> Dict:
    """
    Load and parse JSON file with error handling.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON data
    """
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in {file_path}: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        sys.exit(1)


def main():
    """Main function to run COCO validation."""
    parser = argparse.ArgumentParser(
        description="Validate COCO detection results",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Basic validation
    python box_validation.py --gt ground_truth.json --pred predictions.json

    # With confidence threshold and output file
    python box_validation.py --gt gt.json --pred pred.json --score-thr 0.3 --output results.txt

    # Custom IoU thresholds
    python box_validation.py --gt gt.json --pred pred.json --iou-thrs 0.5 0.75 0.9
        """
    )

    parser.add_argument(
        '--gt', '--ground-truth',
        required=True,
        help='Path to ground truth COCO JSON file'
    )

    parser.add_argument(
        '--pred', '--predictions',
        required=True,
        help='Path to predictions JSON file (list of detection results)'
    )

    parser.add_argument(
        '--score-thr', '--score-threshold',
        type=float,
        default=0.0,
        help='Confidence score threshold for filtering predictions (default: 0.0)'
    )

    parser.add_argument(
        '--iou-thrs', '--iou-thresholds',
        type=float,
        nargs='+',
        default=None,
        help='Custom IoU thresholds for evaluation (default: 0.5:0.95:0.05)'
    )

    parser.add_argument(
        '--max-dets', '--max-detections',
        type=int,
        nargs='+',
        default=None,
        help='Maximum detections per image (default: [1, 10, 100])'
    )

    parser.add_argument(
        '--output', '-o',
        help='Output file path for evaluation report'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    print("COCO Detection Validation")
    print("=" * 50)

    # Load ground truth
    print(f"Loading ground truth: {args.gt}")
    gt_data = load_json_file(args.gt)

    # Validate ground truth format
    print("Validating ground truth format...")
    if not validate_coco_format(gt_data, "ground_truth"):
        print("Ground truth validation failed!")
        sys.exit(1)
    print("✓ Ground truth format is valid")

    # Load predictions
    print(f"Loading predictions: {args.pred}")
    pred_data = load_json_file(args.pred)

    # Validate prediction format
    print("Validating prediction format...")
    is_valid, pred_format = validate_prediction_format(pred_data)
    if not is_valid:
        print("Prediction validation failed!")
        sys.exit(1)
    print("✓ Prediction format is valid")

    # Convert COCO format to list format if needed
    if pred_format == "coco":
        pred_data = convert_coco_to_prediction_list(pred_data)

    # Check if predictions have meaningful scores
    has_scores = all("score" in pred and pred["score"] != 1.0 for pred in pred_data)
    if not has_scores:
        print("Note: Predictions appear to be ground truth annotations (no confidence scores)")
        print("Score filtering will be skipped")

    # Filter predictions by score threshold (only if meaningful scores exist)
    if args.score_thr > 0.0 and has_scores:
        print(f"Filtering predictions with score threshold: {args.score_thr}")
        pred_data = filter_predictions_by_score(pred_data, args.score_thr)
    elif args.score_thr > 0.0 and not has_scores:
        print(f"Warning: Score threshold {args.score_thr} specified but predictions have no meaningful scores")

    # Initialize COCO ground truth
    print("Initializing COCO ground truth...")
    gt_coco = COCO()
    gt_coco.dataset = gt_data
    gt_coco.createIndex()

    # Compute basic statistics
    print("Computing basic statistics...")
    stats = compute_basic_statistics(gt_coco, pred_data)

    if args.verbose:
        print(f"Ground truth images: {stats['gt_images']}")
        print(f"Ground truth annotations: {stats['gt_annotations']}")
        print(f"Predictions: {stats['pred_detections']}")

    # Run COCO evaluation
    coco_eval, metrics = run_coco_evaluation(
        gt_coco,
        pred_data,
        iou_thresholds=args.iou_thrs,
        max_detections=args.max_dets
    )

    if not metrics:
        print("Evaluation failed!")
        sys.exit(1)

    # Compute per-category metrics
    print("Computing per-category metrics...")
    per_cat_metrics = compute_per_category_metrics(coco_eval, gt_coco)

    # Generate and display report
    print("\nGenerating evaluation report...")
    report = generate_evaluation_report(stats, metrics, per_cat_metrics, args.output)

    print(report)

    print("\nValidation completed successfully!")


if __name__ == "__main__":
    main()