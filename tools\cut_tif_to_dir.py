from pathlib import Path
import rasterio
from tqdm import tqdm
import subprocess
import geopandas as gpd


# 将我的结果裁切到测试区面积上
def clip_tif_to_file(input_shape_dir: Path, image: Path, output_dir: Path, method):
    filename = image.stem
    input_img_path = input_shape_dir / f"{filename[5:11]}_{method}_cls_r50.tif"
    with rasterio.open(image) as src:
        bounds = src.bounds

    subprocess.run(
        [
            "gdalwarp",
            "-te",
            str(bounds.left),
            str(bounds.top),
            str(bounds.right),
            str(bounds.bottom),
            "-ts",
            "384",
            "384",
            input_img_path,
            str(output_dir / f"{filename}.tif"),
        ]
    )


if __name__ == "__main__":
    method = "spectral"
    input_img_dir = Path(rf"C:\Projects\tobacco\GraduationProject\DATA\Part-2-ResNet\5_results_2025_{method}_cls_r50_4326")
    image_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch2_384\val\images")
    output_dir = Path(rf"C:\Projects\tobacco\tobacco_det_data\batch2_384\val\images_{method}_result")

    for image in tqdm(list(image_dir.glob("*.tif"))):
        clip_tif_to_file(input_img_dir, image, output_dir, method)
