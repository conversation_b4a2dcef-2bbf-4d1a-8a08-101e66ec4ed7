_base_ = [
    "../base/default_runtime.py",
    "../base/data.py",
    "../base/schedule.py",
]

model = dict(
    type="CenterNet",
    data_preprocessor=dict(
        type="DetDataPreprocessor",
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        bgr_to_rgb=True,
        pad_size_divisor=32,
    ),
    backbone=dict(
        type="ResNet",
        depth=50,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    neck=dict(
        type="FPN",
        in_channels=[256, 512, 1024, 2048],
        out_channels=256,
        start_level=1,
        add_extra_convs="on_output",
        num_outs=5,
        init_cfg=dict(type="Caffe2Xavier", layer="Conv2d"),
        relu_before_extra_convs=True,
    ),
    bbox_head=dict(
        type="CenterNetUpdateHead",
        num_classes=1,
        in_channels=256,
        stacked_convs=4,
        feat_channels=256,
        strides=[8, 16, 32, 64, 128],
        loss_cls=dict(type="GaussianFocalLoss", pos_weight=0.25, neg_weight=0.75, loss_weight=1.0),
        loss_bbox=dict(type="GIoULoss", loss_weight=2.0),
    ),
    train_cfg=None,
    test_cfg=dict(
        nms_pre=1200,
        min_bbox_size=0,
        score_thr=0.05,
        nms=dict(type="nms", iou_threshold=0.6),
        max_per_img=1200,
    ),
)