from pathlib import Path

import geopandas as gpd
from shapely.geometry import Point
import rasterio


def extract_tif_centroids(tif_path):
    with rasterio.open(tif_path) as src:
        width, height = src.width, src.height
        coord = src.xy(width // 2, height // 2)
        return Point(coord[0], coord[1])


if __name__ == "__main__":
    image_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch2_384\train_data\coco\images\val")
    output_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch3_384_valonly\sample_point")
    centroids = []
    for image in image_dir.glob("*.tif"):
        centroids.append(extract_tif_centroids(image))
    gdf = gpd.GeoDataFrame(geometry=centroids, crs="EPSG:4326")
    gdf.to_file(output_dir / "old.arrow", encoding="utf-8", driver="Arrow")
