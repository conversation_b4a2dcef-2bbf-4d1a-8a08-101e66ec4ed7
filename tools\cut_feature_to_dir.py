from pathlib import Path

import geopandas as gpd
import rasterio
from tqdm import tqdm
from multiprocessing import Pool


# 将我的结果裁切到测试区面积上
def clip_feature_to_file(input_shape_dir: Path, image: Path, output_dir: Path, method):
    filename = image.stem
    input_shape_path = input_shape_dir / f"{filename[5:11]}_{method}_cls_r50_bbox.arrow"

    with rasterio.open(image) as src:
        bounds = src.bounds
        left, bottom, right, top = bounds.left, bounds.bottom, bounds.right, bounds.top
        crs = src.crs

    gdf = gpd.read_file(input_shape_path).to_crs(crs)
    gdf = gdf.cx[left:right, bottom:top]
    gdf.to_crs(32647).to_file(output_dir / f"{filename}.arrow", driver="Arrow")


if __name__ == "__main__":
    method = "spectral"
    input_shape_dir = Path(r"C:\Projects\tobacco\GraduationProject\DATA\Part-2-ResNet\5_results_2025_spectral_cls_r50")
    image_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch2_384\images")
    output_dir = Path(r"C:\Projects\tobacco\tobacco_det_data\batch2_384\val\images_spectral_result_bbox")

    files = list(image_dir.glob("*.tif"))

    with Pool(processes=16) as pool:
        pool.starmap(
            clip_feature_to_file,
            [(input_shape_dir, image, output_dir, method) for image in files],
        )
